import * as THREE from 'three';
import ZepApiClient from './zep-api-client.js';

class KnowledgeGraphVisualizer {
  constructor(container) {
    this.container = container;
    this.zepClient = new ZepApiClient();
    this.scene = new THREE.Scene();
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.nodes = new Map();
    this.edges = [];
    this.raycaster = new THREE.Raycaster();
    this.mouse = new THREE.Vector2();
    this.showLabels = true;
    this.selectedNode = null;
    
    this.init();
  }

  init() {
    // Setup renderer
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setClearColor(0x000011, 1);
    this.container.appendChild(this.renderer.domElement);

    // Setup camera
    this.camera.position.set(0, 0, 50);
    this.camera.lookAt(0, 0, 0);

    // Setup lighting
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    this.scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    this.scene.add(directionalLight);

    // Setup controls
    this.setupControls();
    
    // Setup event listeners
    this.setupEventListeners();

    // Start animation loop
    this.animate();
  }

  async visualizeGraph() {
    try {
      console.log('🎨 Starting visualization for Guru1...');
      
      // Test connection first
      const isConnected = await this.zepClient.testConnection();
      console.log('🔗 ZEP connection test:', isConnected ? 'SUCCESS' : 'FAILED');
      
      // Fetch data
      const rawData = await this.zepClient.fetchKnowledgeGraph();
      const graphData = this.transformZepData(rawData);
      
      console.log('📊 Transformed graph data:', graphData);
      
      // Clear existing graph
      this.clearGraph();
      
      // Create nodes
      this.createNodes(graphData.nodes);
      
      // Create edges
      this.createEdges(graphData.edges);
      
      // Position nodes
      this.positionNodes();
      
      // Hide loading indicator
      const loading = document.getElementById('loading');
      if (loading) loading.style.display = 'none';
      
      console.log('✨ Visualization complete!');
      
    } catch (error) {
      console.error('💥 Visualization failed:', error);
      
      // Show mock data instead
      console.log('🎭 Falling back to mock data...');
      const mockData = this.getMockData();
      this.createNodes(mockData.nodes);
      this.createEdges(mockData.edges);
      this.positionNodes();
      
      const loading = document.getElementById('loading');
      if (loading) {
        loading.textContent = 'Using demo data - ZEP API connection failed';
        setTimeout(() => loading.style.display = 'none', 3000);
      }
    }
  }

  transformZepData(zepData) {
    console.log('🔄 Transforming ZEP data:', zepData);
    
    // Handle different possible ZEP response formats
    if (zepData && zepData.nodes && zepData.edges) {
      console.log('📋 Data already in graph format');
      return zepData;
    }
    
    // Handle memory-based data
    if (zepData && (zepData.memories || zepData.results || Array.isArray(zepData))) {
      const memories = zepData.memories || zepData.results || zepData;
      console.log('🧠 Converting memories to graph format:', memories.length, 'memories');
      
      const nodes = memories.map((memory, index) => ({
        id: memory.uuid || memory.id || `memory_${index}`,
        label: this.truncateText(memory.content || memory.message || memory.text || `Memory ${index}`, 50),
        type: 'memory',
        properties: {
          content: memory.content || memory.message || memory.text,
          timestamp: memory.created_at || memory.timestamp,
          metadata: memory.metadata || {}
        }
      }));
      
      // Add user node
      nodes.unshift({
        id: 'guru1_user',
        label: 'Guru1',
        type: 'user',
        properties: { userId: 'Guru1' }
      });
      
      // Create edges connecting user to memories
      const edges = memories.map((memory, index) => ({
        id: `edge_${index}`,
        source: 'guru1_user',
        target: memory.uuid || memory.id || `memory_${index}`,
        label: 'has_memory',
        weight: 1.0
      }));
      
      return { nodes, edges };
    }
    
    console.log('🎭 Using mock data - unrecognized format');
    return this.getMockData();
  }

  getMockData() {
    return {
      nodes: [
        { id: 'guru1', label: 'Guru1', type: 'user', properties: { role: 'main_user' } },
        { id: 'thread1', label: 'guru_card_1905', type: 'thread', properties: { threadId: 'guru_card_1905', date: '7/17/2025' } },
        { id: 'thread2', label: 'guru_card_1904', type: 'thread', properties: { threadId: 'guru_card_1904', date: '7/17/2025' } },
        { id: 'thread3', label: 'guru_card_1903', type: 'thread', properties: { threadId: 'guru_card_1903', date: '7/17/2025' } },
        { id: 'thread4', label: 'guru_card_1902', type: 'thread', properties: { threadId: 'guru_card_1902', date: '7/17/2025' } },
        { id: 'date1', label: '7/17/2025 (1900+ threads)', type: 'date_cluster', properties: { date: '7/17/2025', threadCount: 1900 } },
        { id: 'memory1', label: 'Knowledge Graph Project', type: 'memory', properties: { content: 'Working on 3D visualization' } },
        { id: 'entity1', label: 'ZEP API', type: 'entity', properties: { category: 'technology' } },
        { id: 'entity2', label: 'Three.js', type: 'entity', properties: { category: 'library' } }
      ],
      edges: [
        { id: 'e1', source: 'guru1', target: 'thread1', label: 'created', weight: 1.0 },
        { id: 'e2', source: 'guru1', target: 'thread2', label: 'created', weight: 1.0 },
        { id: 'e3', source: 'guru1', target: 'thread3', label: 'created', weight: 1.0 },
        { id: 'e4', source: 'guru1', target: 'thread4', label: 'created', weight: 1.0 },
        { id: 'e5', source: 'date1', target: 'thread1', label: 'contains', weight: 0.6 },
        { id: 'e6', source: 'date1', target: 'thread2', label: 'contains', weight: 0.6 },
        { id: 'e7', source: 'guru1', target: 'memory1', label: 'has_memory', weight: 1.0 },
        { id: 'e8', source: 'memory1', target: 'entity1', label: 'mentions', weight: 0.8 },
        { id: 'e9', source: 'memory1', target: 'entity2', label: 'uses', weight: 0.9 }
      ]
    };
  }

  truncateText(text, maxLength) {
    if (!text) return 'Unknown';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }

  getNodeColor(type) {
    const colors = {
      'user': 0xFF6B6B,         // Red
      'memory': 0x4ECDC4,       // Teal
      'entity': 0x45B7D1,       // Blue
      'context': 0xFFA07A,      // Orange
      'thread': 0x9B59B6,       // Purple
      'message': 0x2ECC71,      // Green
      'date_cluster': 0xF39C12, // Yellow
      'default': 0x9E9E9E       // Gray
    };
    return colors[type] || colors.default;
  }

  createNodes(nodes) {
    console.log('🔵 Creating', nodes.length, 'nodes');
    
    nodes.forEach(nodeData => {
      const geometry = new THREE.SphereGeometry(2, 16, 16);
      const material = new THREE.MeshLambertMaterial({ 
        color: this.getNodeColor(nodeData.type),
        transparent: true,
        opacity: 0.8
      });
      
      const node = new THREE.Mesh(geometry, material);
      node.userData = nodeData;
      
      // Add text label
      if (this.showLabels) {
        this.addTextLabel(node, nodeData.label);
      }
      
      this.scene.add(node);
      this.nodes.set(nodeData.id, node);
    });
  }

  addTextLabel(node, text) {
    // Create a simple text sprite (placeholder - in real app you'd use proper text rendering)
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = 256;
    canvas.height = 64;

    context.fillStyle = 'rgba(255, 255, 255, 0.8)';
    context.fillRect(0, 0, canvas.width, canvas.height);
    context.fillStyle = 'black';
    context.font = '16px Arial';
    context.textAlign = 'center';
    context.fillText(text, canvas.width / 2, canvas.height / 2);

    const texture = new THREE.CanvasTexture(canvas);
    const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
    const sprite = new THREE.Sprite(spriteMaterial);
    sprite.scale.set(8, 2, 1);
    sprite.position.set(0, 3, 0);

    node.add(sprite);
  }

  createEdges(edges) {
    console.log('🔗 Creating', edges.length, 'edges');

    edges.forEach(edgeData => {
      const sourceNode = this.nodes.get(edgeData.source);
      const targetNode = this.nodes.get(edgeData.target);

      if (sourceNode && targetNode) {
        const geometry = new THREE.BufferGeometry().setFromPoints([
          sourceNode.position,
          targetNode.position
        ]);

        const material = new THREE.LineBasicMaterial({
          color: 0x888888,
          transparent: true,
          opacity: 0.6
        });

        const line = new THREE.Line(geometry, material);
        line.userData = edgeData;

        this.scene.add(line);
        this.edges.push(line);
      }
    });
  }

  positionNodes() {
    console.log('📍 Positioning nodes using force-directed layout');

    const nodes = Array.from(this.nodes.values());
    const radius = 20;

    // Simple circular layout for now
    nodes.forEach((node, index) => {
      const angle = (index / nodes.length) * Math.PI * 2;
      node.position.x = Math.cos(angle) * radius;
      node.position.y = Math.sin(angle) * radius;
      node.position.z = (Math.random() - 0.5) * 10;
    });

    // Update edge positions
    this.updateEdgePositions();
  }

  updateEdgePositions() {
    this.edges.forEach(edge => {
      const sourceNode = this.nodes.get(edge.userData.source);
      const targetNode = this.nodes.get(edge.userData.target);

      if (sourceNode && targetNode) {
        const positions = edge.geometry.attributes.position;
        positions.setXYZ(0, sourceNode.position.x, sourceNode.position.y, sourceNode.position.z);
        positions.setXYZ(1, targetNode.position.x, targetNode.position.y, targetNode.position.z);
        positions.needsUpdate = true;
      }
    });
  }

  clearGraph() {
    // Remove all nodes
    this.nodes.forEach(node => {
      this.scene.remove(node);
    });
    this.nodes.clear();

    // Remove all edges
    this.edges.forEach(edge => {
      this.scene.remove(edge);
    });
    this.edges = [];
  }

  setupControls() {
    // Basic mouse controls for camera
    let isMouseDown = false;
    let mouseX = 0;
    let mouseY = 0;

    this.renderer.domElement.addEventListener('mousedown', (event) => {
      isMouseDown = true;
      mouseX = event.clientX;
      mouseY = event.clientY;
    });

    this.renderer.domElement.addEventListener('mousemove', (event) => {
      if (isMouseDown) {
        const deltaX = event.clientX - mouseX;
        const deltaY = event.clientY - mouseY;

        this.camera.position.x += deltaX * 0.01;
        this.camera.position.y -= deltaY * 0.01;

        mouseX = event.clientX;
        mouseY = event.clientY;
      }
    });

    this.renderer.domElement.addEventListener('mouseup', () => {
      isMouseDown = false;
    });

    // Zoom with mouse wheel
    this.renderer.domElement.addEventListener('wheel', (event) => {
      const zoomSpeed = 0.1;
      this.camera.position.z += event.deltaY * zoomSpeed;
      this.camera.position.z = Math.max(10, Math.min(100, this.camera.position.z));
    });
  }

  setupEventListeners() {
    // Click detection for nodes
    this.renderer.domElement.addEventListener('click', (event) => {
      this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
      this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

      this.raycaster.setFromCamera(this.mouse, this.camera);
      const intersects = this.raycaster.intersectObjects(Array.from(this.nodes.values()));

      if (intersects.length > 0) {
        const clickedNode = intersects[0].object;
        this.selectNode(clickedNode);
      }
    });

    // UI Controls
    document.getElementById('refresh-btn')?.addEventListener('click', () => {
      this.visualizeGraph();
    });

    document.getElementById('reset-view-btn')?.addEventListener('click', () => {
      this.camera.position.set(0, 0, 50);
      this.camera.lookAt(0, 0, 0);
    });

    document.getElementById('toggle-labels-btn')?.addEventListener('click', () => {
      this.showLabels = !this.showLabels;
      this.visualizeGraph(); // Refresh to apply label changes
    });

    // Window resize
    window.addEventListener('resize', () => {
      this.camera.aspect = window.innerWidth / window.innerHeight;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(window.innerWidth, window.innerHeight);
    });
  }

  selectNode(node) {
    // Deselect previous node
    if (this.selectedNode) {
      this.selectedNode.material.emissive.setHex(0x000000);
    }

    // Select new node
    this.selectedNode = node;
    node.material.emissive.setHex(0x444444);

    // Show node information
    this.showNodeInfo(node.userData);
  }

  showNodeInfo(nodeData) {
    const infoPanel = document.getElementById('info-panel');
    const nodeDetails = document.getElementById('node-details');

    if (infoPanel && nodeDetails) {
      let detailsHtml = `
        <h4>${nodeData.label}</h4>
        <p><strong>Type:</strong> ${nodeData.type}</p>
        <p><strong>ID:</strong> ${nodeData.id}</p>
      `;

      // Add type-specific information
      if (nodeData.type === 'thread') {
        detailsHtml += `
          <p><strong>Thread ID:</strong> ${nodeData.properties.threadId || 'N/A'}</p>
          <p><strong>Created:</strong> ${nodeData.properties.createdAt ? new Date(nodeData.properties.createdAt).toLocaleString() : 'N/A'}</p>
          <p><strong>Date:</strong> ${nodeData.properties.date || 'N/A'}</p>
        `;
      } else if (nodeData.type === 'date_cluster') {
        detailsHtml += `
          <p><strong>Date:</strong> ${nodeData.properties.date || 'N/A'}</p>
          <p><strong>Thread Count:</strong> ${nodeData.properties.threadCount || 'N/A'}</p>
        `;
      } else if (nodeData.type === 'user') {
        detailsHtml += `
          <p><strong>User ID:</strong> ${nodeData.properties.userId || 'N/A'}</p>
        `;
      } else if (nodeData.properties.content) {
        detailsHtml += `<p><strong>Content:</strong> ${nodeData.properties.content}</p>`;
      }

      if (nodeData.properties.timestamp) {
        detailsHtml += `<p><strong>Timestamp:</strong> ${new Date(nodeData.properties.timestamp).toLocaleString()}</p>`;
      }

      nodeDetails.innerHTML = detailsHtml;
      infoPanel.style.display = 'block';
    }
  }

  animate() {
    requestAnimationFrame(() => this.animate());

    // Rotate nodes slightly for visual effect
    this.nodes.forEach(node => {
      node.rotation.y += 0.01;
    });

    this.renderer.render(this.scene, this.camera);
  }
}

export default KnowledgeGraphVisualizer;

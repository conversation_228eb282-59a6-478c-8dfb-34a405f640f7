class ZepApiClient {
  constructor() {
    this.apiKey = 'z_1dWlkIjoiOTFhNTNkMWEtNzM4MS00N2NhLTg5MGYtZWVkZTg3MzRhMmFmIn0.vjcRPRVvaeNUgm4CuFmbMFzPdJDOHc_EVhyrRgRcZItWyZlUHajkMqp8zb2_YGLeFBcDEIU8yEpbVOlkYaWOzg';
    this.userId = 'Guru1';
    this.baseUrl = 'https://api.getzep.com';
  }

  async fetchKnowledgeGraph() {
    console.log('🔍 Attempting to fetch knowledge graph for user:', this.userId);

    // First, get threads for the user
    const threads = await this.fetchThreads();
    if (threads && threads.length > 0) {
      console.log(`📋 Found ${threads.length} threads for user ${this.userId}`);

      // Create a graph from thread metadata even if messages are empty
      return this.transformThreadsToGraph(threads);
    }

    console.log('⚠️ No thread data found, trying direct memory endpoints...');
    return await this.fetchMemories();
  }

  async fetchThreads() {
    try {
      console.log('🧵 Fetching threads for user:', this.userId);

      const response = await fetch(`${this.baseUrl}/api/v2/threads`, {
        method: 'GET',
        headers: {
          'Authorization': `Api-Key ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Found ${data.thread?.length || 0} total threads`);

        // Filter threads for our user
        const userThreads = data.thread?.filter(thread => thread.user_id === this.userId) || [];
        console.log(`🎯 Found ${userThreads.length} threads for user ${this.userId}`);

        return userThreads;
      } else {
        const errorText = await response.text();
        console.log(`❌ Failed to fetch threads (${response.status}):`, errorText);
        return null;
      }
    } catch (error) {
      console.log('💥 Error fetching threads:', error.message);
      return null;
    }
  }

  async fetchThreadMessages(threads) {
    const threadData = [];

    for (const thread of threads) {
      try {
        console.log(`💬 Fetching messages for thread: ${thread.thread_id}`);

        const response = await fetch(`${this.baseUrl}/api/v2/threads/${thread.uuid}/messages`, {
          method: 'GET',
          headers: {
            'Authorization': `Api-Key ${this.apiKey}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          console.log(`✅ Got ${data.messages?.length || 0} messages for thread ${thread.thread_id}`);

          if (data.messages && data.messages.length > 0) {
            threadData.push({
              thread: thread,
              messages: data.messages
            });
          }
        } else {
          console.log(`❌ Failed to fetch messages for thread ${thread.thread_id} (${response.status})`);
        }
      } catch (error) {
        console.log(`💥 Error fetching messages for thread ${thread.thread_id}:`, error.message);
      }
    }

    return threadData;
  }

  transformThreadsToGraph(threads) {
    console.log('🔄 Transforming thread data to graph format');

    const nodes = [];
    const edges = [];

    // Add user node
    nodes.push({
      id: 'guru1_user',
      label: 'Guru1',
      type: 'user',
      properties: { userId: this.userId }
    });

    // Group threads by date for better visualization
    const threadsByDate = this.groupThreadsByDate(threads);

    // Process threads (limit to first 20 for performance)
    const threadsToProcess = threads.slice(0, 20);

    threadsToProcess.forEach((thread, threadIndex) => {
      // Add thread node
      const threadNodeId = `thread_${thread.uuid}`;
      const threadDate = new Date(thread.created_at).toLocaleDateString();

      nodes.push({
        id: threadNodeId,
        label: `${thread.thread_id}`,
        type: 'thread',
        properties: {
          threadId: thread.thread_id,
          createdAt: thread.created_at,
          date: threadDate,
          uuid: thread.uuid
        }
      });

      // Connect user to thread
      edges.push({
        id: `edge_user_thread_${threadIndex}`,
        source: 'guru1_user',
        target: threadNodeId,
        label: 'created',
        weight: 1.0
      });
    });

    // Add date cluster nodes
    Object.entries(threadsByDate).forEach(([date, dateThreads], dateIndex) => {
      if (dateThreads.length > 1) {
        const dateNodeId = `date_${date.replace(/\//g, '_')}`;

        nodes.push({
          id: dateNodeId,
          label: `${date} (${dateThreads.length} threads)`,
          type: 'date_cluster',
          properties: {
            date: date,
            threadCount: dateThreads.length
          }
        });

        // Connect date cluster to threads
        dateThreads.slice(0, 20).forEach((thread, index) => {
          const threadNodeId = `thread_${thread.uuid}`;
          edges.push({
            id: `edge_date_thread_${dateIndex}_${index}`,
            source: dateNodeId,
            target: threadNodeId,
            label: 'contains',
            weight: 0.6
          });
        });
      }
    });

    console.log(`📊 Generated graph with ${nodes.length} nodes and ${edges.length} edges`);
    return { nodes, edges };
  }

  groupThreadsByDate(threads) {
    const grouped = {};
    threads.forEach(thread => {
      const date = new Date(thread.created_at).toLocaleDateString();
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(thread);
    });
    return grouped;
  }

  truncateText(text, maxLength) {
    if (!text) return 'Unknown';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }

  async fetchMemories() {
    console.log('🧠 Trying fallback memory endpoints...');

    const memoryEndpoints = [
      `/api/v2/users/${this.userId}/memory`,
      `/api/v2/memory?user_id=${this.userId}`,
      `/users/${this.userId}/memory`,
      `/users/${this.userId}/memories`
    ];

    for (const endpoint of memoryEndpoints) {
      try {
        console.log(`🧠 Trying memory endpoint: ${this.baseUrl}${endpoint}`);

        const response = await fetch(`${this.baseUrl}${endpoint}`, {
          method: 'GET',
          headers: {
            'Authorization': `Api-Key ${this.apiKey}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });

        console.log(`📡 Memory response status for ${endpoint}:`, response.status);

        if (response.ok) {
          const data = await response.json();
          console.log(`✅ Success with memory endpoint: ${endpoint}`, data);
          return data;
        } else {
          const errorText = await response.text();
          console.log(`❌ Failed memory endpoint ${endpoint} (${response.status}):`, errorText);
        }
      } catch (error) {
        console.log(`💥 Network error for memory ${endpoint}:`, error.message);
      }
    }

    throw new Error('All ZEP endpoints failed - check API key and user ID');
  }

  async testConnection() {
    try {
      console.log('🔧 Testing ZEP API connection...');
      const response = await fetch(`${this.baseUrl}/api/v2/threads`, {
        method: 'GET',
        headers: {
          'Authorization': `Api-Key ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('🏥 Connection test response:', response.status);
      return response.ok;
    } catch (error) {
      console.log('💥 Connection test failed:', error.message);
      return false;
    }
  }
}

export default ZepApiClient;

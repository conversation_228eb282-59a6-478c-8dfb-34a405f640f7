// Quick test script to verify ZEP API integration
import ZepApiClient from './src/zep-api-client.js';

async function testZepAPI() {
  console.log('🧪 Testing ZEP API Integration...');
  
  const client = new ZepApiClient();
  
  try {
    // Test connection
    const isConnected = await client.testConnection();
    console.log('🔗 Connection test:', isConnected ? 'SUCCESS' : 'FAILED');
    
    // Test fetching knowledge graph
    const graphData = await client.fetchKnowledgeGraph();
    console.log('📊 Graph data:', graphData);
    
    console.log('✅ ZEP API test completed successfully!');
  } catch (error) {
    console.error('❌ ZEP API test failed:', error);
  }
}

// Run the test
testZepAPI();
